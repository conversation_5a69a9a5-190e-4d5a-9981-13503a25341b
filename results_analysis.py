#!/usr/bin/env python3
"""
Results Analysis and Interpretation for CARREFOUR Hierarchical Model
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

def load_results():
    """Load model results and data"""
    print("Loading results and data...")
    
    # Load model results
    model_results = pd.read_csv('carrefour_model_results.csv', index_col=0)
    cv_results = pd.read_csv('carrefour_cv_results.csv', index_col=0)
    
    # Load processed data
    data = pd.read_csv('carrefour_processed_data.csv')
    
    # Load feature list
    with open('feature_list.txt', 'r') as f:
        features = [line.strip() for line in f.readlines()]
    
    print(f"Model results shape: {model_results.shape}")
    print(f"CV results shape: {cv_results.shape}")
    print(f"Data shape: {data.shape}")
    print(f"Features: {len(features)}")
    
    return model_results, cv_results, data, features

def analyze_model_performance(model_results, cv_results):
    """Analyze model performance"""
    print("\nMODEL PERFORMANCE ANALYSIS")
    print("=" * 50)
    
    print("Full Dataset Performance:")
    print(model_results.round(4))
    
    print("\nCross-Validation Performance:")
    print(cv_results.round(4))
    
    # Best performing model
    best_model_full = model_results['r2'].idxmax()
    best_r2_full = model_results.loc[best_model_full, 'r2']
    
    best_model_cv = cv_results['mean_r2'].idxmax()
    best_r2_cv = cv_results.loc[best_model_cv, 'mean_r2']
    
    print(f"\nBest model on full dataset: {best_model_full} (R² = {best_r2_full:.4f})")
    print(f"Best model on CV: {best_model_cv} (R² = {best_r2_cv:.4f})")
    
    # Model comparison plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Full dataset R²
    model_results['r2'].plot(kind='bar', ax=ax1, color='skyblue')
    ax1.set_title('Model Performance - Full Dataset')
    ax1.set_ylabel('R² Score')
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(axis='y', alpha=0.3)
    
    # CV R² with error bars
    cv_results['mean_r2'].plot(kind='bar', ax=ax2, color='lightcoral', 
                               yerr=cv_results['std_r2'], capsize=5)
    ax2.set_title('Model Performance - Cross Validation')
    ax2.set_ylabel('Mean R² Score')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return best_model_full, best_model_cv

def analyze_feature_insights(data, features):
    """Analyze feature insights and business implications"""
    print("\nFEATURE INSIGHTS AND BUSINESS IMPLICATIONS")
    print("=" * 50)
    
    # Basic feature statistics
    X = data[features]
    y = data['target']
    
    print("Feature Statistics:")
    print(X.describe().round(3))
    
    # Correlation analysis
    correlations = {}
    for feature in features:
        corr = np.corrcoef(X[feature], y)[0, 1]
        correlations[feature] = corr
    
    # Sort by absolute correlation
    sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
    
    print(f"\nFeature Correlations with Target (ABI Promo Uplift - Rel):")
    for feature, corr in sorted_corr:
        direction = "positive" if corr > 0 else "negative"
        strength = "strong" if abs(corr) > 0.5 else "moderate" if abs(corr) > 0.3 else "weak"
        print(f"{feature:30s}: {corr:6.3f} ({strength} {direction})")
    
    # Business insights
    print(f"\nBUSINESS INSIGHTS:")
    print("=" * 30)
    
    # ABI Mechanic analysis
    if 'ABI_Mechanic_LV' in features and 'ABI_Mechanic_FID' in features:
        lv_effect = correlations.get('ABI_Mechanic_LV', 0)
        fid_effect = correlations.get('ABI_Mechanic_FID', 0)
        
        print(f"1. PROMOTIONAL MECHANICS:")
        print(f"   - LV (Low Value) vs No NIP: {lv_effect:+.3f} correlation")
        print(f"   - FID vs No NIP: {fid_effect:+.3f} correlation")
        
        if lv_effect > fid_effect:
            print(f"   → LV promotions show better uplift than FID promotions")
        else:
            print(f"   → FID promotions show better uplift than LV promotions")
    
    # Duration analysis
    if 'ABI_Duration_Days' in features:
        duration_effect = correlations.get('ABI_Duration_Days', 0)
        print(f"\n2. PROMOTION DURATION:")
        print(f"   - Duration correlation: {duration_effect:+.3f}")
        if duration_effect > 0:
            print(f"   → Longer promotions tend to have higher uplift")
        else:
            print(f"   → Shorter promotions tend to have higher uplift")
    
    # Coverage analysis
    if 'ABI Coverage' in features:
        coverage_effect = correlations.get('ABI Coverage', 0)
        print(f"\n3. STORE COVERAGE:")
        print(f"   - Coverage correlation: {coverage_effect:+.3f}")
        if coverage_effect > 0:
            print(f"   → Higher store coverage leads to better uplift")
        else:
            print(f"   → Lower store coverage shows better uplift")
    
    # Timing analysis
    timing_features = ['Same Week', '1 wk after', '2 wk after', '1 wk before', '2 wk before']
    timing_effects = {f: correlations.get(f, 0) for f in timing_features if f in features}
    
    if timing_effects:
        print(f"\n4. COMPETITIVE TIMING:")
        for timing, effect in timing_effects.items():
            print(f"   - {timing}: {effect:+.3f}")
        
        best_timing = max(timing_effects.items(), key=lambda x: abs(x[1]))
        print(f"   → Strongest timing effect: {best_timing[0]} ({best_timing[1]:+.3f})")
    
    # Temperature analysis
    if 'Avg Temp' in features:
        temp_effect = correlations.get('Avg Temp', 0)
        print(f"\n5. SEASONALITY (Temperature):")
        print(f"   - Temperature correlation: {temp_effect:+.3f}")
        if temp_effect > 0:
            print(f"   → Warmer weather associated with higher uplift")
        else:
            print(f"   → Cooler weather associated with higher uplift")
    
    return correlations, sorted_corr

def generate_recommendations(correlations, model_results):
    """Generate business recommendations"""
    print(f"\nBUSINESS RECOMMENDATIONS")
    print("=" * 40)
    
    # Get best model performance
    best_model = model_results['r2'].idxmax()
    best_r2 = model_results.loc[best_model, 'r2']
    
    print(f"Model Performance: {best_model} achieved R² = {best_r2:.3f}")
    print(f"This explains {best_r2*100:.1f}% of the variance in promotional uplift.\n")
    
    # Top factors
    sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
    top_factors = sorted_corr[:3]
    
    print("TOP 3 UPLIFT DRIVERS:")
    for i, (factor, corr) in enumerate(top_factors, 1):
        direction = "increases" if corr > 0 else "decreases"
        print(f"{i}. {factor}: {direction} uplift (correlation: {corr:+.3f})")
    
    print(f"\nACTIONABLE RECOMMENDATIONS:")
    
    # Mechanic recommendations
    lv_corr = correlations.get('ABI_Mechanic_LV', 0)
    fid_corr = correlations.get('ABI_Mechanic_FID', 0)
    
    if lv_corr > 0.1:
        print(f"1. PRIORITIZE LV PROMOTIONS: LV mechanics show positive uplift effect")
    elif fid_corr > 0.1:
        print(f"1. PRIORITIZE FID PROMOTIONS: FID mechanics show positive uplift effect")
    else:
        print(f"1. MECHANIC OPTIMIZATION: Consider testing different promotional mechanics")
    
    # Duration recommendations
    duration_corr = correlations.get('ABI_Duration_Days', 0)
    if duration_corr > 0.1:
        print(f"2. EXTEND DURATION: Longer promotions tend to drive higher uplift")
    elif duration_corr < -0.1:
        print(f"2. OPTIMIZE DURATION: Shorter, more focused promotions may be more effective")
    else:
        print(f"2. DURATION TESTING: Current duration seems optimal, test variations")
    
    # Coverage recommendations
    coverage_corr = correlations.get('ABI Coverage', 0)
    if coverage_corr > 0.1:
        print(f"3. EXPAND COVERAGE: Increase store coverage for better uplift")
    elif coverage_corr < -0.1:
        print(f"3. FOCUS COVERAGE: Selective store coverage may be more effective")
    else:
        print(f"3. COVERAGE OPTIMIZATION: Current coverage strategy appears balanced")
    
    # Model limitations
    print(f"\nMODEL LIMITATIONS:")
    print(f"- Sample size: 117 observations (consider collecting more data)")
    print(f"- R² = {best_r2:.3f} indicates {(1-best_r2)*100:.1f}% of variance unexplained")
    print(f"- External factors (competitor actions, market conditions) not fully captured")

def create_summary_report(model_results, cv_results, correlations, data):
    """Create comprehensive summary report"""
    print(f"\nEXECUTIVE SUMMARY REPORT")
    print("=" * 50)
    
    # Dataset overview
    print(f"DATASET OVERVIEW:")
    print(f"- Retailer: CARREFOUR only")
    print(f"- Observations: {len(data)}")
    print(f"- Target Variable: ABI Promo Uplift - Rel")
    print(f"- Target Range: {data['target'].min():.2f} to {data['target'].max():.2f}")
    print(f"- Average Uplift: {data['target'].mean():.2f}x")
    
    # Model performance
    best_model = model_results['r2'].idxmax()
    best_r2 = model_results.loc[best_model, 'r2']
    best_rmse = model_results.loc[best_model, 'rmse']
    
    print(f"\nMODEL PERFORMANCE:")
    print(f"- Best Model: {best_model}")
    print(f"- R² Score: {best_r2:.3f} ({best_r2*100:.1f}% variance explained)")
    print(f"- RMSE: {best_rmse:.3f}")
    print(f"- Model Status: {'Good' if best_r2 > 0.5 else 'Moderate' if best_r2 > 0.3 else 'Needs Improvement'}")
    
    # Key insights
    sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)
    
    print(f"\nKEY FINDINGS:")
    print(f"- Most Important Factor: {sorted_corr[0][0]} (correlation: {sorted_corr[0][1]:+.3f})")
    print(f"- Promotional Mechanics: {'LV preferred' if correlations.get('ABI_Mechanic_LV', 0) > correlations.get('ABI_Mechanic_FID', 0) else 'FID preferred'}")
    print(f"- Duration Effect: {'Positive' if correlations.get('ABI_Duration_Days', 0) > 0 else 'Negative'}")
    print(f"- Coverage Effect: {'Positive' if correlations.get('ABI Coverage', 0) > 0 else 'Negative'}")

def main():
    """Main analysis pipeline"""
    print("CARREFOUR HIERARCHICAL MODEL - RESULTS ANALYSIS")
    print("=" * 60)
    
    # Load results
    model_results, cv_results, data, features = load_results()
    
    # Analyze performance
    best_full, best_cv = analyze_model_performance(model_results, cv_results)
    
    # Analyze features and insights
    correlations, sorted_corr = analyze_feature_insights(data, features)
    
    # Generate recommendations
    generate_recommendations(correlations, model_results)
    
    # Create summary report
    create_summary_report(model_results, cv_results, correlations, data)
    
    print(f"\nAnalysis completed! Check generated plots and insights above.")
    
    return {
        'model_results': model_results,
        'cv_results': cv_results,
        'correlations': correlations,
        'best_models': {'full': best_full, 'cv': best_cv}
    }

if __name__ == "__main__":
    analysis_results = main()

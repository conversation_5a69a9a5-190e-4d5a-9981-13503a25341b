#!/usr/bin/env python3
"""
Data preprocessing script for Carrefour hierarchical model
Handles feature engineering, dummy encoding, and data preparation
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

def load_and_filter_data():
    """Load data and filter for Carrefour"""
    print("Loading and filtering data for Carrefour...")
    
    # Load the data
    df = pd.read_csv('greater than 10 new_test_output_ads_v3.csv')
    
    # Filter for Carrefour data (all variants)
    carrefour_data = df[df['Retailer'].str.contains('CARREFOUR', case=False, na=False)].copy()
    
    print(f"Original dataset: {df.shape}")
    print(f"Carrefour dataset: {carrefour_data.shape}")
    print(f"Carrefour retailers: {carrefour_data['Retailer'].unique()}")
    
    return carrefour_data

def create_duration_feature(df):
    """Create ABI_Duration_Days from start and end dates"""
    print("Creating duration feature...")
    
    # Convert dates
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])
    
    # Calculate duration in days
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days + 1
    
    print(f"Duration stats: {df['ABI_Duration_Days'].describe()}")
    return df

def prepare_features(df):
    """Prepare features for modeling"""
    print("Preparing features...")
    
    # Define the allowed columns as specified by user
    allowed_columns = [
        'ABI_Duration_Days',
        'ABI Mechanic', 
        'Same Week',
        '1 wk after',
        '2 wk after',
        '1 wk before', 
        '2 wk before',
        'Avg Temp',
        'ABI vs Segment PTC Index Agg',
        'ABI Coverage'
    ]
    
    # Target variable
    target_col = 'ABI MS Promo Uplift - rel'
    
    # Check which columns exist
    existing_features = []
    for col in allowed_columns:
        if col in df.columns:
            existing_features.append(col)
        else:
            print(f"Warning: Column '{col}' not found in dataset")
    
    print(f"Using features: {existing_features}")
    
    # Create feature dataframe
    X = df[existing_features].copy()
    y = df[target_col].copy()
    
    # Add retailer as a hierarchical grouping variable
    X['Retailer'] = df['Retailer']
    
    print(f"Feature matrix shape: {X.shape}")
    print(f"Target variable shape: {y.shape}")
    
    return X, y, existing_features

def handle_categorical_variables(X, features_list):
    """Handle categorical variables with dummy encoding"""
    print("Handling categorical variables...")
    
    # Identify categorical and numerical columns
    categorical_cols = []
    numerical_cols = []
    
    for col in features_list:
        if col in X.columns:
            if X[col].dtype == 'object' or col == 'ABI Mechanic':
                categorical_cols.append(col)
            else:
                numerical_cols.append(col)
    
    print(f"Categorical columns: {categorical_cols}")
    print(f"Numerical columns: {numerical_cols}")
    
    # Handle ABI Mechanic with dummy encoding (base = 'No NIP' as specified)
    if 'ABI Mechanic' in categorical_cols:
        print(f"ABI Mechanic values: {X['ABI Mechanic'].value_counts()}")
        
        # Create dummy variables with 'No NIP' as base (dropped)
        mechanic_dummies = pd.get_dummies(X['ABI Mechanic'], prefix='ABI_Mechanic', drop_first=False)
        
        # Drop 'No NIP' column to use it as base
        if 'ABI_Mechanic_No NIP' in mechanic_dummies.columns:
            mechanic_dummies = mechanic_dummies.drop('ABI_Mechanic_No NIP', axis=1)
            print("Using 'No NIP' as base category for ABI Mechanic")
        
        # Add dummy variables to X
        X = pd.concat([X, mechanic_dummies], axis=1)
        
        # Remove original categorical column
        X = X.drop('ABI Mechanic', axis=1)
        
        # Update features list
        features_list = [col for col in features_list if col != 'ABI Mechanic']
        features_list.extend(mechanic_dummies.columns.tolist())
        
        print(f"Created dummy variables: {mechanic_dummies.columns.tolist()}")
    
    return X, features_list

def analyze_data_quality(X, y):
    """Analyze data quality and missing values"""
    print("\nData Quality Analysis:")
    print("=" * 50)
    
    # Missing values
    missing_data = X.isnull().sum()
    if missing_data.sum() > 0:
        print("Missing values:")
        print(missing_data[missing_data > 0])
    else:
        print("No missing values found")
    
    # Data types
    print(f"\nData types:")
    print(X.dtypes)
    
    # Basic statistics
    print(f"\nNumerical features statistics:")
    numerical_cols = X.select_dtypes(include=[np.number]).columns
    print(X[numerical_cols].describe())
    
    # Target variable analysis
    print(f"\nTarget variable statistics:")
    print(y.describe())
    
    return X, y

def save_processed_data(X, y, features_list):
    """Save processed data for modeling"""
    print("Saving processed data...")
    
    # Combine features and target
    processed_data = X.copy()
    processed_data['target'] = y
    
    # Save to CSV
    processed_data.to_csv('carrefour_processed_data.csv', index=False)
    
    # Save feature list
    with open('feature_list.txt', 'w') as f:
        for feature in features_list:
            f.write(f"{feature}\n")
    
    print(f"Saved processed data: {processed_data.shape}")
    print(f"Saved {len(features_list)} features to feature_list.txt")
    
    return processed_data

def main():
    """Main preprocessing pipeline"""
    print("Starting data preprocessing for Carrefour hierarchical model")
    print("=" * 60)
    
    # Load and filter data
    df = load_and_filter_data()
    
    # Create duration feature
    df = create_duration_feature(df)
    
    # Prepare features
    X, y, features_list = prepare_features(df)
    
    # Handle categorical variables
    X, features_list = handle_categorical_variables(X, features_list)
    
    # Analyze data quality
    X, y = analyze_data_quality(X, y)
    
    # Save processed data
    processed_data = save_processed_data(X, y, features_list)
    
    print("\nPreprocessing completed successfully!")
    print(f"Final dataset shape: {processed_data.shape}")
    print(f"Features: {features_list}")
    
    return X, y, features_list

if __name__ == "__main__":
    X, y, features = main()

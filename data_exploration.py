#!/usr/bin/env python3
"""
Data exploration script for Carrefour hierarchical model
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def explore_data():
    """Explore the dataset and understand its structure"""
    
    # Load the data
    print("Loading data...")
    df = pd.read_csv('greater than 10 new_test_output_ads_v3.csv')
    
    print(f"Dataset shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Filter for Carrefour data
    carrefour_data = df[df['Retailer'].str.contains('CARREFOUR', case=False, na=False)]
    print(f"\nCarrefour data shape: {carrefour_data.shape}")
    print(f"Unique Carrefour retailers: {carrefour_data['Retailer'].unique()}")
    
    # Check the specified columns
    required_columns = [
        'ABI_Duration_Days',  # This might not exist, let's check
        'ABI Mechanic',
        'Same Week',
        '1 wk after',
        '2 wk after', 
        '1 wk before',
        '2 wk before',
        'Avg Temp',
        'ABI vs Segment PTC Index Agg',
        'ABI Coverage'  # This should be ABI_Coverage
    ]
    
    # Check which columns exist
    existing_columns = []
    missing_columns = []
    
    for col in required_columns:
        if col in df.columns:
            existing_columns.append(col)
        else:
            missing_columns.append(col)
    
    print(f"\nExisting required columns: {existing_columns}")
    print(f"Missing columns: {missing_columns}")
    
    # Check for similar column names
    print("\nAll columns in dataset:")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")
    
    # Check target variable
    target_col = 'ABI MS Promo Uplift - rel'
    if target_col in df.columns:
        print(f"\nTarget variable '{target_col}' found!")
        print(f"Target variable stats for Carrefour:")
        print(carrefour_data[target_col].describe())
    else:
        print(f"\nTarget variable '{target_col}' not found!")
    
    # Check ABI Mechanic values
    if 'ABI Mechanic' in df.columns:
        print(f"\nABI Mechanic unique values:")
        print(df['ABI Mechanic'].value_counts())
        print(f"\nABI Mechanic values in Carrefour data:")
        print(carrefour_data['ABI Mechanic'].value_counts())
    
    # Check for duration-related columns
    duration_cols = [col for col in df.columns if 'duration' in col.lower() or 'days' in col.lower()]
    print(f"\nDuration-related columns: {duration_cols}")
    
    # Check date columns to calculate duration
    date_cols = [col for col in df.columns if any(word in col.lower() for word in ['start', 'end', 'date'])]
    print(f"Date-related columns: {date_cols}")
    
    # If we have start and end dates, calculate duration
    if 'ABI Start' in df.columns and 'ABI End' in df.columns:
        print("\nCalculating duration from start and end dates...")
        df['ABI Start'] = pd.to_datetime(df['ABI Start'])
        df['ABI End'] = pd.to_datetime(df['ABI End'])
        df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days + 1
        print(f"Duration stats: {df['ABI_Duration_Days'].describe()}")
    
    return df, carrefour_data

if __name__ == "__main__":
    df, carrefour_data = explore_data()

#!/usr/bin/env python3
"""
Hierarchical Model for CARREFOUR only
Optimized for smaller dataset (117 observations)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearReg<PERSON>, <PERSON>, Lasso
from sklearn.model_selection import cross_val_score, KFold, train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load processed CARREFOUR data"""
    print("Loading CARREFOUR processed data...")
    
    df = pd.read_csv('carrefour_processed_data.csv')
    
    # Separate features and target
    target_col = 'target'
    feature_cols = [col for col in df.columns if col not in [target_col, 'Retailer']]
    
    X = df[feature_cols]
    y = df[target_col]
    
    print(f"Data shape: {X.shape}")
    print(f"Features: {feature_cols}")
    print(f"Target stats: mean={y.mean():.3f}, std={y.std():.3f}")
    
    return X, y, df

def analyze_feature_importance_correlation(X, y):
    """Analyze feature importance and correlations"""
    print("\nFeature Analysis")
    print("=" * 40)
    
    # Calculate correlations with target
    correlations = {}
    for col in X.columns:
        if X[col].dtype in ['int64', 'float64']:
            corr, p_value = stats.pearsonr(X[col], y)
            correlations[col] = {'correlation': corr, 'p_value': p_value}
        else:
            # For boolean columns, use point-biserial correlation
            corr, p_value = stats.pointbiserialr(X[col], y)
            correlations[col] = {'correlation': corr, 'p_value': p_value}
    
    # Sort by absolute correlation
    sorted_corr = sorted(correlations.items(), key=lambda x: abs(x[1]['correlation']), reverse=True)
    
    print("Feature correlations with target (sorted by absolute value):")
    for feature, stats_dict in sorted_corr:
        corr = stats_dict['correlation']
        p_val = stats_dict['p_value']
        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
        print(f"{feature:30s}: {corr:6.3f} (p={p_val:.3f}) {significance}")
    
    return correlations

class CarrefourHierarchicalModel:
    """
    Hierarchical model for CARREFOUR with multiple approaches
    """
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.feature_names = None
        self.is_fitted = False
        
    def fit(self, X, y):
        """Fit multiple models"""
        print("\nTraining Carrefour Hierarchical Models...")
        
        self.feature_names = X.columns.tolist()
        
        # Scale features for linear models
        X_scaled = self.scaler.fit_transform(X)
        
        # 1. Linear Regression (baseline)
        print("Training Linear Regression...")
        self.models['linear'] = LinearRegression()
        self.models['linear'].fit(X_scaled, y)
        
        # 2. Ridge Regression (regularized)
        print("Training Ridge Regression...")
        self.models['ridge'] = Ridge(alpha=1.0)
        self.models['ridge'].fit(X_scaled, y)
        
        # 3. Lasso Regression (feature selection)
        print("Training Lasso Regression...")
        self.models['lasso'] = Lasso(alpha=0.1)
        self.models['lasso'].fit(X_scaled, y)
        
        # 4. Random Forest (non-linear, smaller for small dataset)
        print("Training Random Forest...")
        self.models['random_forest'] = RandomForestRegressor(
            n_estimators=50,  # Reduced for small dataset
            max_depth=5,      # Prevent overfitting
            min_samples_split=5,
            min_samples_leaf=3,
            random_state=42
        )
        self.models['random_forest'].fit(X, y)  # Use original features
        
        # 5. Ensemble (weighted average)
        print("Creating ensemble...")
        
        self.is_fitted = True
        print("Model training completed!")
        
    def predict(self, X):
        """Make predictions"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        X_scaled = self.scaler.transform(X)
        
        predictions = {}
        predictions['linear'] = self.models['linear'].predict(X_scaled)
        predictions['ridge'] = self.models['ridge'].predict(X_scaled)
        predictions['lasso'] = self.models['lasso'].predict(X_scaled)
        predictions['random_forest'] = self.models['random_forest'].predict(X)
        
        # Ensemble prediction (weighted average)
        weights = {'linear': 0.2, 'ridge': 0.3, 'lasso': 0.2, 'random_forest': 0.3}
        ensemble_pred = np.zeros(len(X))
        
        for model_name, weight in weights.items():
            ensemble_pred += weight * predictions[model_name]
        
        predictions['ensemble'] = ensemble_pred
        
        return predictions
    
    def get_feature_importance(self):
        """Get feature importance from different models"""
        importance_dict = {}
        
        # Linear model coefficients
        if 'linear' in self.models:
            importance_dict['linear_coef'] = np.abs(self.models['linear'].coef_)
        
        # Ridge coefficients
        if 'ridge' in self.models:
            importance_dict['ridge_coef'] = np.abs(self.models['ridge'].coef_)
        
        # Lasso coefficients
        if 'lasso' in self.models:
            importance_dict['lasso_coef'] = np.abs(self.models['lasso'].coef_)
        
        # Random Forest importance
        if 'random_forest' in self.models:
            importance_dict['rf_importance'] = self.models['random_forest'].feature_importances_
        
        return importance_dict

def evaluate_model(y_true, y_pred, model_name="Model"):
    """Evaluate model performance"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    print(f"\n{model_name} Performance:")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE:  {mae:.4f}")
    print(f"R²:   {r2:.4f}")
    
    return {'rmse': rmse, 'mae': mae, 'r2': r2, 'mse': mse}

def cross_validate_models(X, y, cv_folds=5):
    """Perform cross-validation"""
    print(f"\nPerforming {cv_folds}-fold Cross-Validation...")
    
    cv = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
    cv_results = {}
    
    for fold, (train_idx, val_idx) in enumerate(cv.split(X, y)):
        print(f"\nFold {fold + 1}/{cv_folds}")
        
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # Train model
        model = CarrefourHierarchicalModel()
        model.fit(X_train, y_train)
        
        # Predict
        predictions = model.predict(X_val)
        
        # Evaluate each model
        for model_name, y_pred in predictions.items():
            if model_name not in cv_results:
                cv_results[model_name] = {'rmse': [], 'mae': [], 'r2': []}
            
            scores = evaluate_model(y_val, y_pred, f"Fold {fold+1} - {model_name}")
            cv_results[model_name]['rmse'].append(scores['rmse'])
            cv_results[model_name]['mae'].append(scores['mae'])
            cv_results[model_name]['r2'].append(scores['r2'])
    
    # Print CV summary
    print(f"\nCross-Validation Summary:")
    print("=" * 50)
    
    cv_summary = {}
    for model_name, scores in cv_results.items():
        mean_rmse = np.mean(scores['rmse'])
        std_rmse = np.std(scores['rmse'])
        mean_r2 = np.mean(scores['r2'])
        std_r2 = np.std(scores['r2'])
        
        cv_summary[model_name] = {
            'mean_rmse': mean_rmse,
            'std_rmse': std_rmse,
            'mean_r2': mean_r2,
            'std_r2': std_r2
        }
        
        print(f"{model_name:15s}: RMSE = {mean_rmse:.4f} ± {std_rmse:.4f}, R² = {mean_r2:.4f} ± {std_r2:.4f}")
    
    return cv_summary

def plot_results(y_true, predictions, feature_importance, feature_names):
    """Plot model results"""
    
    # 1. Prediction vs Actual plots
    n_models = len(predictions)
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, (model_name, y_pred) in enumerate(predictions.items()):
        if i < len(axes):
            axes[i].scatter(y_true, y_pred, alpha=0.6)
            axes[i].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
            axes[i].set_xlabel('Actual')
            axes[i].set_ylabel('Predicted')
            axes[i].set_title(f'{model_name.replace("_", " ").title()}')
            
            # Add R² to plot
            r2 = r2_score(y_true, y_pred)
            axes[i].text(0.05, 0.95, f'R² = {r2:.3f}', transform=axes[i].transAxes, 
                        bbox=dict(boxstyle="round", facecolor='wheat', alpha=0.5))
    
    # Hide unused subplots
    for i in range(n_models, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('model_predictions.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. Feature importance comparison
    if feature_importance:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, (imp_name, importance) in enumerate(feature_importance.items()):
            if i < len(axes):
                sorted_idx = np.argsort(importance)[::-1]
                sorted_features = [feature_names[j] for j in sorted_idx]
                sorted_importance = importance[sorted_idx]
                
                axes[i].barh(range(len(sorted_features)), sorted_importance)
                axes[i].set_yticks(range(len(sorted_features)))
                axes[i].set_yticklabels(sorted_features)
                axes[i].set_xlabel('Importance')
                axes[i].set_title(f'{imp_name.replace("_", " ").title()}')
                axes[i].invert_yaxis()
        
        # Hide unused subplots
        for i in range(len(feature_importance), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('feature_importance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """Main modeling pipeline"""
    print("CARREFOUR Hierarchical Model Training")
    print("=" * 50)
    
    # Load data
    X, y, df = load_data()
    
    # Analyze features
    correlations = analyze_feature_importance_correlation(X, y)
    
    # Train model
    model = CarrefourHierarchicalModel()
    model.fit(X, y)
    
    # Make predictions
    predictions = model.predict(X)
    
    # Evaluate models
    print("\nFull Dataset Model Evaluation:")
    print("=" * 40)
    
    results = {}
    for model_name, y_pred in predictions.items():
        results[model_name] = evaluate_model(y, y_pred, model_name.replace('_', ' ').title())
    
    # Cross-validation
    cv_summary = cross_validate_models(X, y)
    
    # Feature importance
    feature_importance = model.get_feature_importance()
    
    # Plot results
    plot_results(y, predictions, feature_importance, X.columns.tolist())
    
    # Save results
    results_df = pd.DataFrame(results).T
    results_df.to_csv('carrefour_model_results.csv')
    
    cv_df = pd.DataFrame(cv_summary).T
    cv_df.to_csv('carrefour_cv_results.csv')
    
    print(f"\nModeling completed!")
    print(f"Results saved to 'carrefour_model_results.csv' and 'carrefour_cv_results.csv'")
    
    return model, results, cv_summary, correlations

if __name__ == "__main__":
    model, results, cv_summary, correlations = main()

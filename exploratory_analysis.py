#!/usr/bin/env python3
"""
Exploratory Data Analysis for Carrefour hierarchical model
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

def load_processed_data():
    """Load the processed data"""
    print("Loading processed data...")
    
    df = pd.read_csv('carrefour_processed_data.csv')
    
    # Separate features and target
    target_col = 'target'
    feature_cols = [col for col in df.columns if col not in [target_col, 'Retailer']]
    
    X = df[feature_cols]
    y = df[target_col]
    retailer = df['Retailer'] if 'Retailer' in df.columns else None
    
    print(f"Data shape: {df.shape}")
    print(f"Features: {len(feature_cols)}")
    print(f"Target variable: {target_col}")
    
    return X, y, retailer, df

def analyze_target_distribution(y, retailer=None):
    """Analyze target variable distribution"""
    print("\nTarget Variable Analysis")
    print("=" * 40)
    
    # Basic statistics
    print(f"Target variable statistics:")
    print(y.describe())
    
    # Create plots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Target Variable: ABI Promo Uplift - Rel', fontsize=16)
    
    # Histogram
    axes[0, 0].hist(y, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('Distribution of Target Variable')
    axes[0, 0].set_xlabel('ABI Promo Uplift - Rel')
    axes[0, 0].set_ylabel('Frequency')
    
    # Box plot
    axes[0, 1].boxplot(y)
    axes[0, 1].set_title('Box Plot of Target Variable')
    axes[0, 1].set_ylabel('ABI Promo Uplift - Rel')
    
    # Q-Q plot for normality
    stats.probplot(y, dist="norm", plot=axes[1, 0])
    axes[1, 0].set_title('Q-Q Plot (Normality Check)')
    
    # Target by retailer if available
    if retailer is not None:
        retailer_data = pd.DataFrame({'target': y, 'retailer': retailer})
        sns.boxplot(data=retailer_data, x='retailer', y='target', ax=axes[1, 1])
        axes[1, 1].set_title('Target by Retailer')
        axes[1, 1].tick_params(axis='x', rotation=45)
    else:
        axes[1, 1].text(0.5, 0.5, 'Retailer data not available', 
                       ha='center', va='center', transform=axes[1, 1].transAxes)
    
    plt.tight_layout()
    plt.savefig('target_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Normality test
    shapiro_stat, shapiro_p = stats.shapiro(y)
    print(f"\nShapiro-Wilk normality test:")
    print(f"Statistic: {shapiro_stat:.4f}, p-value: {shapiro_p:.4f}")
    
    if shapiro_p < 0.05:
        print("Target variable is NOT normally distributed (p < 0.05)")
    else:
        print("Target variable appears normally distributed (p >= 0.05)")

def analyze_feature_distributions(X):
    """Analyze feature distributions"""
    print("\nFeature Distribution Analysis")
    print("=" * 40)
    
    # Separate numerical and categorical features
    numerical_features = X.select_dtypes(include=[np.number]).columns.tolist()
    categorical_features = X.select_dtypes(include=['bool']).columns.tolist()
    
    print(f"Numerical features: {numerical_features}")
    print(f"Categorical features: {categorical_features}")
    
    # Plot numerical features
    if numerical_features:
        n_num_features = len(numerical_features)
        n_cols = 3
        n_rows = (n_num_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature in enumerate(numerical_features):
            row = i // n_cols
            col = i % n_cols
            
            axes[row, col].hist(X[feature], bins=20, alpha=0.7, edgecolor='black')
            axes[row, col].set_title(f'Distribution of {feature}')
            axes[row, col].set_xlabel(feature)
            axes[row, col].set_ylabel('Frequency')
        
        # Hide empty subplots
        for i in range(n_num_features, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('numerical_features_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    # Plot categorical features
    if categorical_features:
        n_cat_features = len(categorical_features)
        n_cols = 2
        n_rows = (n_cat_features + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(12, 4*n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        for i, feature in enumerate(categorical_features):
            row = i // n_cols
            col = i % n_cols
            
            value_counts = X[feature].value_counts()
            axes[row, col].bar(value_counts.index.astype(str), value_counts.values)
            axes[row, col].set_title(f'Distribution of {feature}')
            axes[row, col].set_xlabel(feature)
            axes[row, col].set_ylabel('Count')
        
        # Hide empty subplots
        for i in range(n_cat_features, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('categorical_features_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()

def analyze_correlations(X, y):
    """Analyze correlations between features and target"""
    print("\nCorrelation Analysis")
    print("=" * 40)
    
    # Create correlation matrix with target
    data_for_corr = X.copy()
    data_for_corr['target'] = y
    
    # Calculate correlation matrix
    corr_matrix = data_for_corr.corr()
    
    # Plot correlation heatmap
    plt.figure(figsize=(12, 10))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    plt.title('Feature Correlation Matrix')
    plt.tight_layout()
    plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Feature-target correlations
    target_corr = corr_matrix['target'].drop('target').sort_values(key=abs, ascending=False)
    
    print("Feature correlations with target (sorted by absolute value):")
    for feature, corr in target_corr.items():
        print(f"{feature:30s}: {corr:6.3f}")
    
    # Plot feature-target correlations
    plt.figure(figsize=(10, 6))
    colors = ['red' if x < 0 else 'blue' for x in target_corr.values]
    bars = plt.barh(range(len(target_corr)), target_corr.values, color=colors, alpha=0.7)
    plt.yticks(range(len(target_corr)), target_corr.index)
    plt.xlabel('Correlation with Target')
    plt.title('Feature Correlations with Target Variable')
    plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    plt.grid(axis='x', alpha=0.3)
    plt.tight_layout()
    plt.savefig('feature_target_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return target_corr

def analyze_feature_relationships(X, y):
    """Analyze relationships between key features and target"""
    print("\nFeature-Target Relationship Analysis")
    print("=" * 40)
    
    # Get numerical features for scatter plots
    numerical_features = X.select_dtypes(include=[np.number]).columns.tolist()
    
    if len(numerical_features) > 0:
        # Create scatter plots for top numerical features
        n_features = min(6, len(numerical_features))
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        for i, feature in enumerate(numerical_features[:n_features]):
            axes[i].scatter(X[feature], y, alpha=0.6)
            axes[i].set_xlabel(feature)
            axes[i].set_ylabel('Target')
            axes[i].set_title(f'Target vs {feature}')
            
            # Add trend line
            z = np.polyfit(X[feature], y, 1)
            p = np.poly1d(z)
            axes[i].plot(X[feature], p(X[feature]), "r--", alpha=0.8)
        
        # Hide empty subplots
        for i in range(n_features, 6):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('feature_target_relationships.png', dpi=300, bbox_inches='tight')
        plt.show()

def generate_summary_report(X, y, target_corr, retailer=None):
    """Generate summary report"""
    print("\nSUMMARY REPORT")
    print("=" * 50)
    
    print(f"Dataset Overview:")
    print(f"- Total observations: {len(y)}")
    print(f"- Number of features: {X.shape[1]}")
    print(f"- Target variable range: {y.min():.3f} to {y.max():.3f}")
    print(f"- Target variable mean: {y.mean():.3f}")
    print(f"- Target variable std: {y.std():.3f}")
    
    if retailer is not None:
        print(f"- Retailer distribution:")
        retailer_counts = pd.Series(retailer).value_counts()
        for ret, count in retailer_counts.items():
            print(f"  {ret}: {count} ({count/len(retailer)*100:.1f}%)")
    
    print(f"\nTop 5 features correlated with target:")
    for i, (feature, corr) in enumerate(target_corr.head().items(), 1):
        print(f"{i}. {feature}: {corr:.3f}")
    
    print(f"\nData Quality:")
    missing_counts = X.isnull().sum()
    if missing_counts.sum() > 0:
        print("Features with missing values:")
        for feature, count in missing_counts[missing_counts > 0].items():
            print(f"- {feature}: {count} ({count/len(X)*100:.1f}%)")
    else:
        print("- No missing values detected")

def main():
    """Main EDA pipeline"""
    print("Starting Exploratory Data Analysis for Carrefour")
    print("=" * 60)
    
    # Load data
    X, y, retailer, df = load_processed_data()
    
    # Analyze target distribution
    analyze_target_distribution(y, retailer)
    
    # Analyze feature distributions
    analyze_feature_distributions(X)
    
    # Analyze correlations
    target_corr = analyze_correlations(X, y)
    
    # Analyze feature relationships
    analyze_feature_relationships(X, y)
    
    # Generate summary report
    generate_summary_report(X, y, target_corr, retailer)
    
    print("\nEDA completed! Check the generated plots and analysis.")
    
    return X, y, target_corr

if __name__ == "__main__":
    X, y, correlations = main()

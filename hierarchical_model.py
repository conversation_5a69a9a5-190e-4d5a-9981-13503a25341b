#!/usr/bin/env python3
"""
Hierarchical Model Implementation for Carrefour
Implements mixed effects models and ensemble approaches for hierarchical modeling
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.model_selection import cross_val_score, KFold, GroupKFold
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Try to import statsmodels for mixed effects
try:
    import statsmodels.api as sm
    import statsmodels.formula.api as smf
    STATSMODELS_AVAILABLE = True
except ImportError:
    print("Warning: statsmodels not available. Mixed effects models will be skipped.")
    STATSMODELS_AVAILABLE = False

def load_data():
    """Load processed data"""
    print("Loading processed data...")
    
    df = pd.read_csv('carrefour_processed_data.csv')
    
    # Separate features and target
    target_col = 'target'
    feature_cols = [col for col in df.columns if col not in [target_col, 'Retailer']]
    
    X = df[feature_cols]
    y = df[target_col]
    
    # Get retailer for grouping
    if 'Retailer' in df.columns:
        groups = df['Retailer']
    else:
        # Create dummy groups if retailer not available
        groups = pd.Series(['Group1'] * len(df))
    
    print(f"Data shape: {X.shape}")
    print(f"Target shape: {y.shape}")
    print(f"Groups: {groups.value_counts()}")
    
    return X, y, groups, df

class HierarchicalModelEnsemble:
    """
    Hierarchical model ensemble combining multiple approaches
    """
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def fit(self, X, y, groups=None):
        """Fit multiple hierarchical models"""
        print("Fitting hierarchical model ensemble...")
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        # 1. Random Forest (handles non-linearity and interactions)
        print("Training Random Forest...")
        self.models['random_forest'] = RandomForestRegressor(
            n_estimators=100, 
            max_depth=10, 
            min_samples_split=5,
            random_state=42
        )
        self.models['random_forest'].fit(X_scaled, y)
        
        # 2. Gradient Boosting (sequential learning)
        print("Training Gradient Boosting...")
        self.models['gradient_boosting'] = GradientBoostingRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        self.models['gradient_boosting'].fit(X_scaled, y)
        
        # 3. Ridge Regression (regularized linear model)
        print("Training Ridge Regression...")
        self.models['ridge'] = Ridge(alpha=1.0)
        self.models['ridge'].fit(X_scaled, y)
        
        # 4. Group-specific models (simple hierarchical approach)
        print("Training group-specific models...")
        self.models['group_models'] = {}
        
        if groups is not None:
            for group in groups.unique():
                group_mask = groups == group
                if group_mask.sum() > 5:  # Only if enough samples
                    group_model = Ridge(alpha=0.5)
                    group_model.fit(X_scaled[group_mask], y[group_mask])
                    self.models['group_models'][group] = group_model
        
        # 5. Mixed Effects Model (if statsmodels available)
        if STATSMODELS_AVAILABLE and groups is not None:
            try:
                print("Training Mixed Effects Model...")
                self._fit_mixed_effects(X_scaled_df, y, groups)
            except Exception as e:
                print(f"Mixed effects model failed: {e}")
        
        self.is_fitted = True
        print("Model ensemble training completed!")
        
    def _fit_mixed_effects(self, X, y, groups):
        """Fit mixed effects model using statsmodels"""
        # Prepare data for mixed effects
        data = X.copy()
        data['target'] = y
        data['group'] = groups
        
        # Create formula (using first few features to avoid overfitting)
        feature_names = X.columns[:5]  # Use top 5 features
        formula = f"target ~ {' + '.join(feature_names)}"
        
        try:
            # Fit mixed effects model
            model = smf.mixedlm(formula, data, groups=data['group'])
            self.models['mixed_effects'] = model.fit()
            print("Mixed effects model fitted successfully")
        except Exception as e:
            print(f"Mixed effects fitting failed: {e}")
    
    def predict(self, X, groups=None):
        """Make predictions using ensemble"""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        X_scaled = self.scaler.transform(X)
        predictions = {}
        
        # Get predictions from each model
        predictions['random_forest'] = self.models['random_forest'].predict(X_scaled)
        predictions['gradient_boosting'] = self.models['gradient_boosting'].predict(X_scaled)
        predictions['ridge'] = self.models['ridge'].predict(X_scaled)
        
        # Group-specific predictions
        if groups is not None and 'group_models' in self.models:
            group_preds = np.zeros(len(X))
            for i, group in enumerate(groups):
                if group in self.models['group_models']:
                    group_preds[i] = self.models['group_models'][group].predict(X_scaled[i:i+1])[0]
                else:
                    # Fallback to ridge if group not seen
                    group_preds[i] = self.models['ridge'].predict(X_scaled[i:i+1])[0]
            predictions['group_specific'] = group_preds
        
        # Mixed effects predictions
        if 'mixed_effects' in self.models:
            try:
                X_df = pd.DataFrame(X_scaled, columns=X.columns)
                X_df['group'] = groups if groups is not None else 'Group1'
                predictions['mixed_effects'] = self.models['mixed_effects'].predict(X_df)
            except:
                pass
        
        # Ensemble prediction (weighted average)
        weights = {
            'random_forest': 0.3,
            'gradient_boosting': 0.3,
            'ridge': 0.2,
            'group_specific': 0.2
        }
        
        ensemble_pred = np.zeros(len(X))
        total_weight = 0
        
        for model_name, weight in weights.items():
            if model_name in predictions:
                ensemble_pred += weight * predictions[model_name]
                total_weight += weight
        
        ensemble_pred /= total_weight
        predictions['ensemble'] = ensemble_pred
        
        return predictions
    
    def get_feature_importance(self):
        """Get feature importance from tree-based models"""
        importance_dict = {}
        
        if 'random_forest' in self.models:
            importance_dict['random_forest'] = self.models['random_forest'].feature_importances_
        
        if 'gradient_boosting' in self.models:
            importance_dict['gradient_boosting'] = self.models['gradient_boosting'].feature_importances_
        
        return importance_dict

def evaluate_model(y_true, y_pred, model_name="Model"):
    """Evaluate model performance"""
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    print(f"\n{model_name} Performance:")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE:  {mae:.4f}")
    print(f"R²:   {r2:.4f}")
    
    return {'rmse': rmse, 'mae': mae, 'r2': r2}

def cross_validate_hierarchical(X, y, groups, cv_folds=5):
    """Perform cross-validation with group awareness"""
    print(f"\nPerforming {cv_folds}-fold cross-validation...")
    
    # Use GroupKFold to ensure groups don't split across folds
    if len(groups.unique()) >= cv_folds:
        cv = GroupKFold(n_splits=cv_folds)
        cv_splits = list(cv.split(X, y, groups))
    else:
        # Fallback to regular KFold if not enough groups
        cv = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        cv_splits = list(cv.split(X, y))
    
    cv_scores = {'rmse': [], 'mae': [], 'r2': []}
    
    for fold, (train_idx, val_idx) in enumerate(cv_splits):
        print(f"Fold {fold + 1}/{cv_folds}")
        
        # Split data
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        groups_train = groups.iloc[train_idx] if groups is not None else None
        groups_val = groups.iloc[val_idx] if groups is not None else None
        
        # Train model
        model = HierarchicalModelEnsemble()
        model.fit(X_train, y_train, groups_train)
        
        # Predict
        predictions = model.predict(X_val, groups_val)
        y_pred = predictions['ensemble']
        
        # Evaluate
        scores = evaluate_model(y_val, y_pred, f"Fold {fold + 1}")
        cv_scores['rmse'].append(scores['rmse'])
        cv_scores['mae'].append(scores['mae'])
        cv_scores['r2'].append(scores['r2'])
    
    # Print CV summary
    print(f"\nCross-Validation Summary:")
    print(f"RMSE: {np.mean(cv_scores['rmse']):.4f} ± {np.std(cv_scores['rmse']):.4f}")
    print(f"MAE:  {np.mean(cv_scores['mae']):.4f} ± {np.std(cv_scores['mae']):.4f}")
    print(f"R²:   {np.mean(cv_scores['r2']):.4f} ± {np.std(cv_scores['r2']):.4f}")
    
    return cv_scores

def plot_feature_importance(model, feature_names):
    """Plot feature importance"""
    importance_dict = model.get_feature_importance()
    
    if not importance_dict:
        print("No feature importance available")
        return
    
    fig, axes = plt.subplots(1, len(importance_dict), figsize=(6*len(importance_dict), 6))
    if len(importance_dict) == 1:
        axes = [axes]
    
    for i, (model_name, importance) in enumerate(importance_dict.items()):
        # Sort features by importance
        sorted_idx = np.argsort(importance)[::-1]
        sorted_features = [feature_names[i] for i in sorted_idx]
        sorted_importance = importance[sorted_idx]
        
        axes[i].barh(range(len(sorted_features)), sorted_importance)
        axes[i].set_yticks(range(len(sorted_features)))
        axes[i].set_yticklabels(sorted_features)
        axes[i].set_xlabel('Importance')
        axes[i].set_title(f'{model_name.replace("_", " ").title()} Feature Importance')
        axes[i].invert_yaxis()
    
    plt.tight_layout()
    plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main hierarchical modeling pipeline"""
    print("Starting Hierarchical Model Training for Carrefour")
    print("=" * 60)
    
    # Load data
    X, y, groups, df = load_data()
    
    # Train full model
    print("\nTraining full hierarchical model ensemble...")
    model = HierarchicalModelEnsemble()
    model.fit(X, y, groups)
    
    # Make predictions on full dataset
    predictions = model.predict(X, groups)
    
    # Evaluate each model component
    print("\nModel Component Evaluation:")
    print("=" * 40)
    
    results = {}
    for model_name, y_pred in predictions.items():
        results[model_name] = evaluate_model(y, y_pred, model_name.replace('_', ' ').title())
    
    # Cross-validation
    cv_scores = cross_validate_hierarchical(X, y, groups)
    
    # Feature importance
    plot_feature_importance(model, X.columns.tolist())
    
    # Save results
    results_df = pd.DataFrame(results).T
    results_df.to_csv('model_results.csv')
    
    print("\nHierarchical modeling completed!")
    print("Results saved to 'model_results.csv'")
    
    return model, results, cv_scores

if __name__ == "__main__":
    model, results, cv_scores = main()
